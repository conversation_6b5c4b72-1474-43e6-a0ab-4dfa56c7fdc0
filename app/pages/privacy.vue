<script setup lang="ts">
const { t } = useI18n()

useSeoMeta({
  title: `${t('privacy.title')} - Imagen`,
  description: t('privacy.description'),
  ogTitle: `${t('privacy.title')} - Imagen`,
  ogDescription: t('privacy.description')
})
</script>

<template>
  <UPage>
    <UContainer>
      <UPageHeader
        :title="$t('privacy.title')"
        :description="$t('privacy.description')"
      />
    </UContainer>
    <UContainer class="mt-4">
      <div class="prose dark:prose-invert max-w-none">
        <div class="mb-6 text-sm text-gray-600 dark:text-gray-400">
          <p><strong>{{ $t("privacy.lastUpdated") }}</strong> {{ $t("privacy.lastUpdatedDate") }}</p>
        </div>

        <div class="mb-8">
          <p class="text-lg">{{ $t("privacy.introduction") }}</p>
        </div>

        <h2>{{ $t("privacy.informationWeCollect") }}</h2>
        <h3>{{ $t("privacy.personalInformation") }}</h3>
        <ul>
          <li v-for="item in $t('privacy.personalInformationList', { returnObjects: true })" :key="item">
            {{ item }}
          </li>
        </ul>

        <h3>{{ $t("privacy.usageInformation") }}</h3>
        <ul>
          <li v-for="item in $t('privacy.usageInformationList', { returnObjects: true })" :key="item">
            {{ item }}
          </li>
        </ul>

        <h3>{{ $t("privacy.technicalInformation") }}</h3>
        <ul>
          <li v-for="item in $t('privacy.technicalInformationList', { returnObjects: true })" :key="item">
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("privacy.howWeUseInformation") }}</h2>
        <ul>
          <li v-for="item in $t('privacy.howWeUseInformationList', { returnObjects: true })" :key="item">
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("privacy.informationSharing") }}</h2>
        <p>{{ $t("privacy.informationSharingIntro") }}</p>
        <ul>
          <li v-for="item in $t('privacy.informationSharingList', { returnObjects: true })" :key="item">
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("privacy.dataSecurity") }}</h2>
        <p>{{ $t("privacy.dataSecurityDescription") }}</p>
        <ul>
          <li v-for="item in $t('privacy.dataSecurityMeasures', { returnObjects: true })" :key="item">
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("privacy.dataRetention") }}</h2>
        <p>{{ $t("privacy.dataRetentionDescription") }}</p>

        <h2>{{ $t("privacy.yourRights") }}</h2>
        <p>{{ $t("privacy.yourRightsDescription") }}</p>
        <ul>
          <li v-for="item in $t('privacy.yourRightsList', { returnObjects: true })" :key="item">
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("privacy.cookies") }}</h2>
        <p>{{ $t("privacy.cookiesDescription") }}</p>

        <h2>{{ $t("privacy.thirdPartyServices") }}</h2>
        <p>{{ $t("privacy.thirdPartyServicesDescription") }}</p>

        <h2>{{ $t("privacy.childrenPrivacy") }}</h2>
        <p>{{ $t("privacy.childrenPrivacyDescription") }}</p>

        <h2>{{ $t("privacy.internationalTransfers") }}</h2>
        <p>{{ $t("privacy.internationalTransfersDescription") }}</p>

        <h2>{{ $t("privacy.policyChanges") }}</h2>
        <p>{{ $t("privacy.policyChangesDescription") }}</p>

        <h2>{{ $t("privacy.contactUs") }}</h2>
        <p>{{ $t("privacy.contactUsDescription") }}</p>
        <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <p><strong>{{ $t("privacy.contactEmail") }}</strong> <EMAIL></p>
          <p><strong>{{ $t("privacy.contactAddress") }}</strong></p>
          <p>{{ $t("privacy.companyAddress") }}</p>
        </div>
      </div>
    </UContainer>
  </UPage>
</template>
