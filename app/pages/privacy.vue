<script setup lang="ts">
const { t } = useI18n()

// Computed properties for arrays to ensure proper reactivity
const personalInformationList = computed(() => t('privacy.personalInformationList', { returnObjects: true }) as unknown as string[])
const usageInformationList = computed(() => t('privacy.usageInformationList', { returnObjects: true }) as unknown as string[])
const technicalInformationList = computed(() => t('privacy.technicalInformationList', { returnObjects: true }) as unknown as string[])
const howWeUseInformationList = computed(() => t('privacy.howWeUseInformationList', { returnObjects: true }) as unknown as string[])
const informationSharingList = computed(() => t('privacy.informationSharingList', { returnObjects: true }) as unknown as string[])
const dataSecurityMeasures = computed(() => t('privacy.dataSecurityMeasures', { returnObjects: true }) as unknown as string[])
const yourRightsList = computed(() => t('privacy.yourRightsList', { returnObjects: true }) as unknown as string[])

useSeoMeta({
  title: `${t('privacy.title')} - Imagen`,
  description: t('privacy.description'),
  ogTitle: `${t('privacy.title')} - Imagen`,
  ogDescription: t('privacy.description')
})
</script>

<template>
  <UPage>
    <UContainer>
      <UPageHeader
        :title="$t('privacy.title')"
        :description="$t('privacy.description')"
      />
    </UContainer>
    <UContainer class="mt-4">
      <div class="prose dark:prose-invert max-w-none">
        <div class="mb-6 text-sm text-gray-600 dark:text-gray-400">
          <p><strong>{{ $t("privacy.lastUpdated") }}</strong> {{ $t("privacy.lastUpdatedDate") }}</p>
        </div>

        <div class="mb-8">
          <p class="text-lg">{{ $t("privacy.introduction") }}</p>
        </div>

        <h2>{{ $t("privacy.informationWeCollect") }}</h2>
        <h3>{{ $t("privacy.personalInformation") }}</h3>
        <ul>
          <li v-for="(item, index) in personalInformationList" :key="index">
            {{ item }}
          </li>
        </ul>

        <h3>{{ $t("privacy.usageInformation") }}</h3>
        <ul>
          <li v-for="(item, index) in usageInformationList" :key="index">
            {{ item }}
          </li>
        </ul>

        <h3>{{ $t("privacy.technicalInformation") }}</h3>
        <ul>
          <li v-for="(item, index) in technicalInformationList" :key="index">
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("privacy.howWeUseInformation") }}</h2>
        <ul>
          <li v-for="(item, index) in howWeUseInformationList" :key="index">
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("privacy.informationSharing") }}</h2>
        <p>{{ $t("privacy.informationSharingIntro") }}</p>
        <ul>
          <li v-for="(item, index) in informationSharingList" :key="index">
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("privacy.dataSecurity") }}</h2>
        <p>{{ $t("privacy.dataSecurityDescription") }}</p>
        <ul>
          <li v-for="(item, index) in dataSecurityMeasures" :key="index">
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("privacy.dataRetention") }}</h2>
        <p>{{ $t("privacy.dataRetentionDescription") }}</p>

        <h2>{{ $t("privacy.yourRights") }}</h2>
        <p>{{ $t("privacy.yourRightsDescription") }}</p>
        <ul>
          <li v-for="(item, index) in yourRightsList" :key="index">
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("privacy.cookies") }}</h2>
        <p>{{ $t("privacy.cookiesDescription") }}</p>

        <h2>{{ $t("privacy.thirdPartyServices") }}</h2>
        <p>{{ $t("privacy.thirdPartyServicesDescription") }}</p>

        <h2>{{ $t("privacy.childrenPrivacy") }}</h2>
        <p>{{ $t("privacy.childrenPrivacyDescription") }}</p>

        <h2>{{ $t("privacy.internationalTransfers") }}</h2>
        <p>{{ $t("privacy.internationalTransfersDescription") }}</p>

        <h2>{{ $t("privacy.policyChanges") }}</h2>
        <p>{{ $t("privacy.policyChangesDescription") }}</p>

        <h2>{{ $t("privacy.contactUs") }}</h2>
        <p>{{ $t("privacy.contactUsDescription") }}</p>
        <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <p><strong>{{ $t("privacy.contactEmail") }}</strong> <EMAIL></p>
          <p><strong>{{ $t("privacy.contactAddress") }}</strong></p>
          <p>{{ $t("privacy.companyAddress") }}</p>
        </div>
      </div>
    </UContainer>
  </UPage>
</template>
