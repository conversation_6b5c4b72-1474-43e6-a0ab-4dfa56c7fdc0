<script setup lang="ts">
const { t } = useI18n()

useSeoMeta({
  title: `${t('privacy.title')} - Imagen`,
  description: t('privacy.description'),
  ogTitle: `${t('privacy.title')} - Imagen`,
  ogDescription: t('privacy.description')
})
</script>

<template>
  <UPage>
    <UContainer>
      <UPageHeader
        :title="$t('privacy.title')"
        :description="$t('privacy.description')"
      />
    </UContainer>
    <UContainer class="mt-4">
      <div class="prose dark:prose-invert max-w-none">
        <div class="mb-6 text-sm text-gray-600 dark:text-gray-400">
          <p>
            <strong>{{ $t("privacy.lastUpdated") }}</strong>
            {{ $t("privacy.lastUpdatedDate") }}
          </p>
        </div>

        <div class="mb-8">
          <p class="text-lg">
            {{ $t("privacy.introduction") }}
          </p>
        </div>

        <h2>{{ $t("privacy.informationWeCollect") }}</h2>
        <p>{{ $t("privacy.informationCollectionDescription") }}</p>

        <h2>{{ $t("privacy.creditCalculation") }}</h2>
        <p>{{ $t("privacy.creditCalculationDescription") }}</p>

        <h2>{{ $t("privacy.paymentSecurity") }}</h2>
        <p>{{ $t("privacy.paymentSecurityDescription") }}</p>

        <h2>{{ $t("privacy.emailNotification") }}</h2>
        <p>{{ $t("privacy.emailNotificationDescription") }}</p>

        <h2>{{ $t("privacy.dataSecurity") }}</h2>
        <p>{{ $t("privacy.dataSecurityDescription") }}</p>

        <h2>{{ $t("privacy.thirdPartyServices") }}</h2>
        <p>{{ $t("privacy.thirdPartyServicesDescription") }}</p>

        <h2>{{ $t("privacy.cookies") }}</h2>
        <p>{{ $t("privacy.cookiesDescription") }}</p>

        <h2>{{ $t("privacy.thirdPartyLinks") }}</h2>
        <p>{{ $t("privacy.thirdPartyLinksDescription") }}</p>

        <h2>{{ $t("privacy.childrenPrivacy") }}</h2>
        <p>{{ $t("privacy.childrenPrivacyDescription") }}</p>

        <h2>{{ $t("privacy.policyChanges") }}</h2>
        <p>{{ $t("privacy.policyChangesDescription") }}</p>

        <h2>{{ $t("privacy.commercialUse") }}</h2>
        <p>{{ $t("privacy.commercialUseDescription") }}</p>

        <h2>{{ $t("privacy.otherPeoplePrivacy") }}</h2>
        <p>{{ $t("privacy.otherPeoplePrivacyDescription") }}</p>

        <h2>{{ $t("privacy.unsubscribe") }}</h2>
        <p>{{ $t("privacy.unsubscribeDescription") }}</p>

        <h2>{{ $t("privacy.contactUs") }}</h2>
      </div>
    </UContainer>
  </UPage>
</template>
