<script setup lang="ts">
const { t } = useI18n()

// Computed properties for arrays to ensure proper reactivity
const userAccountsResponsibilities = computed(() => t('terms.userAccountsResponsibilities', { returnObjects: true }) as unknown as string[])
const paymentTerms = computed(() => t('terms.paymentTerms', { returnObjects: true }) as unknown as string[])
const permittedUseList = computed(() => t('terms.permittedUseList', { returnObjects: true }) as unknown as string[])
const prohibitedUseList = computed(() => t('terms.prohibitedUseList', { returnObjects: true }) as unknown as string[])
const userContentRights = computed(() => t('terms.userContentRights', { returnObjects: true }) as unknown as string[])
const terminationReasons = computed(() => t('terms.terminationReasons', { returnObjects: true }) as unknown as string[])
const disclaimersList = computed(() => t('terms.disclaimersList', { returnObjects: true }) as unknown as string[])

useSeoMeta({
  title: `${t('terms.title')} - Imagen`,
  description: t('terms.description'),
  ogTitle: `${t('terms.title')} - Imagen`,
  ogDescription: t('terms.description')
})
</script>

<template>
  <UPage>
    <UContainer>
      <UPageHeader
        :title="$t('terms.title')"
        :description="$t('terms.description')"
      />
    </UContainer>

    <UContainer class="mt-4">
      <div class="prose dark:prose-invert max-w-none">
        <div class="mb-6 text-sm text-gray-600 dark:text-gray-400">
          <p><strong>{{ $t("terms.lastUpdated") }}</strong> {{ $t("terms.lastUpdatedDate") }}</p>
        </div>

        <div class="mb-8">
          <p class="text-lg">
            {{ $t("terms.introduction") }}
          </p>
        </div>

        <h2>{{ $t("terms.acceptanceOfTerms") }}</h2>
        <p>{{ $t("terms.acceptanceOfTermsDescription") }}</p>
        <p>{{ $t("terms.acceptanceOfTermsDetails") }}</p>

        <h2>{{ $t("terms.userAccounts") }}</h2>
        <p>{{ $t("terms.userAccountsDescription") }}</p>
        <ul>
          <li
            v-for="(item, index) in userAccountsResponsibilities"
            :key="index"
          >
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("terms.paymentAndBilling") }}</h2>
        <p>{{ $t("terms.paymentAndBillingDescription") }}</p>
        <ul>
          <li
            v-for="(item, index) in paymentTerms"
            :key="index"
          >
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("terms.useOfService") }}</h2>
        <p>{{ $t("terms.serviceUsageDescription") }}</p>
        <h3>{{ $t("terms.permittedUse") }}</h3>
        <ul>
          <li
            v-for="(item, index) in permittedUseList"
            :key="index"
          >
            {{ item }}
          </li>
        </ul>

        <h3>{{ $t("terms.prohibitedUse") }}</h3>
        <ul>
          <li
            v-for="(item, index) in prohibitedUseList"
            :key="index"
          >
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("terms.intellectualProperty") }}</h2>
        <p>{{ $t("terms.intellectualPropertyDescription") }}</p>
        <h3>{{ $t("terms.ourIntellectualProperty") }}</h3>
        <p>{{ $t("terms.ourIntellectualPropertyDescription") }}</p>

        <h3>{{ $t("terms.userGeneratedContent") }}</h3>
        <p>{{ $t("terms.userGeneratedContentDescription") }}</p>
        <ul>
          <li
            v-for="(item, index) in userContentRights"
            :key="index"
          >
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("terms.limitationOfLiability") }}</h2>
        <p>{{ $t("terms.limitationOfLiabilityDescription") }}</p>

        <h2>{{ $t("terms.disclaimers") }}</h2>
        <p>{{ $t("terms.disclaimersDescription") }}</p>
        <ul>
          <li
            v-for="(item, index) in disclaimersList"
            :key="index"
          >
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("terms.termination") }}</h2>
        <p>{{ $t("terms.terminationDescription") }}</p>
        <h3>{{ $t("terms.terminationByUser") }}</h3>
        <p>{{ $t("terms.terminationByUserDescription") }}</p>

        <h3>{{ $t("terms.terminationByUs") }}</h3>
        <p>{{ $t("terms.terminationByUsDescription") }}</p>
        <ul>
          <li
            v-for="(item, index) in terminationReasons"
            :key="index"
          >
            {{ item }}
          </li>
        </ul>

        <h2>{{ $t("terms.governingLaw") }}</h2>
        <p>{{ $t("terms.governingLawDescription") }}</p>

        <h2>{{ $t("terms.clarificationOpenAI") }}</h2>
        <p>{{ $t("terms.clarificationOpenAIDescription") }}</p>

        <h2>{{ $t("terms.contactUsTerms") }}</h2>
        <p>{{ $t("terms.contactUsTermsDescription") }}</p>
        <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <p><strong>{{ $t("terms.contactEmail") }}</strong> <EMAIL></p>
          <p><strong>{{ $t("terms.contactAddress") }}</strong></p>
          <p>{{ $t("terms.companyAddress") }}</p>
        </div>
      </div>
    </UContainer>
  </UPage>
</template>
